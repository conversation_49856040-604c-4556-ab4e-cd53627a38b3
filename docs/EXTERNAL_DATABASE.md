# Configuración de Base de Datos Externa

Este documento describe cómo configurar y usar la conexión a la base de datos externa PostgreSQL para obtener información de productos.

## Configuración

### Variables de Entorno

Agrega las siguientes variables de entorno a tu archivo `.env`:

```env
# External Database Configuration
EXTERNAL_POSTGRES_DB_URL=jdbc:postgresql://************:5432/cuora
EXTERNAL_POSTGRES_USER=user
EXTERNAL_POSTGRES_PASSWORD=1234
```

### Estructura de las Tablas Externas

La aplicación espera que la base de datos externa tenga las siguientes tablas:

```sql
CREATE TABLE product (
    product_id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    uom_id INTEGER NOT NULL,
    isactive BOOLEAN NOT NULL DEFAULT true
);

CREATE TABLE productprice (
    product_id INTEGER NOT NULL,
    pricelist_version_id VARCHAR(50) NOT NULL,
    pricelist DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (product_id, pricelist_version_id),
    FOREIGN KEY (product_id) REFERENCES product(product_id)
);
```

La consulta utilizada es:
```sql
SELECT p.product_id, p.name, p.description, p2.pricelist as "price", p.uom_id, p.isactive
FROM product p
INNER JOIN productprice p2 ON p.product_id = p2.product_id
WHERE p2.pricelist_version_id = 'lst1'
```

## API Endpoints

### Obtener todos los productos externos

```http
GET /api/external-products
```

**Respuesta:**
```json
[
    {
        "productId": 0,
        "name": "Producto Ejemplo 0",
        "description": "Descripción del producto 0",
        "price": "5.25",
        "uomId": 1,
        "isActive": true
    },
    {
        "productId": 1,
        "name": "Producto Ejemplo 1",
        "description": "Descripción del producto 1",
        "price": "25.50",
        "uomId": 1,
        "isActive": true
    },
    {
        "productId": 2,
        "name": "Producto Ejemplo 2",
        "description": null,
        "price": "15.75",
        "uomId": 2,
        "isActive": true
    }
]
```

### Sincronizar productos (IMPORTANTE: debe ir antes que /{id})

```http
POST /api/external-products/sync
```

**Descripción:** Limpia la tabla interna de productos y sincroniza todos los productos de la base de datos externa.

**Mapeo de campos:**
- `codigo` = `product_id`
- `nombre` = `name`
- `descripcion` = `description`
- `precio_unitario` = `price`
- `unidad_medida_id` = `uom_id`
- `activo` = `isactive`
- `tipo_iva_id` = 5 (valor fijo)

**Respuesta:**
```json
{
    "message": "Sincronización completada",
    "count": 4
}
```

### Obtener un producto específico

```http
GET /api/external-products/{id}
```

**Respuesta:**
```json
{
    "productId": 1,
    "name": "Producto Ejemplo 1",
    "description": "Descripción del producto 1",
    "price": "25.50",
    "uomId": 1,
    "isActive": true
}
```

### Obtener productos internos sincronizados

```http
GET /productos
```

**Descripción:** Obtiene todos los productos de la tabla interna (productos sincronizados).

**Respuesta:**
```json
{
    "productos": [
        {
            "codigo": 1,
            "nombre": "Producto Ejemplo 1",
            "descripcion": "Descripción del producto 1",
            "unidadMedidaId": 1,
            "tipoIvaId": 5,
            "categoriaId": null,
            "precioUnitario": 25.50,
            "stockActual": 0,
            "activo": true
        }
    ]
}
```

## Arquitectura

La implementación sigue el patrón de arquitectura hexagonal:

- **Dominio**: `ExternalProduct` - Modelo de dominio para productos externos
- **Puerto de Entrada**: `ExternalProductService` - Interfaz del servicio
- **Puerto de Salida**: `ExternalProductRepositoryPort` - Interfaz del repositorio
- **Adaptador**: `JdbcExternalProductRepository` - Implementación usando JDBC
- **Controlador**: `ExternalProductController` - API REST

## Configuración de Conexión

La conexión a la base de datos externa se configura en `ExternalDatabase.kt` con las siguientes características:

- Pool de conexiones: 5 conexiones máximo
- AutoCommit: true (para consultas de solo lectura)
- Timeout de conexión: 30 segundos
- Timeout de idle: 10 minutos
- Tiempo de vida máximo: 30 minutos

## Manejo de Errores

La aplicación maneja los siguientes tipos de errores:

- **SQLException**: Errores de conexión o consulta SQL
- **Validation**: Validación de parámetros de entrada
- **NotFound**: Producto no encontrado
- **Routing**: Conflictos de rutas (ej: "sync" interpretado como ID)

Todos los errores se devuelven como respuestas JSON con el código de estado HTTP apropiado.

## Testing

Se incluyen tests unitarios que usan un repositorio mock para verificar la funcionalidad sin depender de la base de datos externa.

Para ejecutar los tests:

```bash
./gradlew test
```
