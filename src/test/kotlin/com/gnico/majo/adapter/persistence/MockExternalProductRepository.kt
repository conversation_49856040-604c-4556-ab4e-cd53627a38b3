package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import java.math.BigDecimal

/**
 * Implementación mock del repositorio de productos externos para tests
 */
class MockExternalProductRepository : ExternalProductRepositoryPort {

    private val mockProducts = listOf(
        ExternalProduct.create(0, "Producto Externo 0", "Descripción del producto 0", BigDecimal("5.25"), 1, true),
        ExternalProduct.create(1, "Producto Externo 1", "Descripción del producto 1", BigDecimal("10.50"), 1, true),
        ExternalProduct.create(2, "Producto Externo 2", "Descripción del producto 2", BigDecimal("25.75"), 2, true),
        ExternalProduct.create(3, "Producto Externo 3", null, BigDecimal("15.00"), 1, false)
    )

    override suspend fun findAllProducts(): List<ExternalProduct> {
        return mockProducts
    }

    override suspend fun findProductById(productId: Int): ExternalProduct? {
        return mockProducts.find { it.productId == productId }
    }
}
