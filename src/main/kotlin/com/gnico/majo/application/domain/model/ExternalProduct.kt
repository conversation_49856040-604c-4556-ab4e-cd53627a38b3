package com.gnico.majo.application.domain.model

import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * Representa un producto obtenido de la base de datos externa
 */
@Serializable
data class ExternalProduct(
    val productId: Int,
    val name: String,
    val description: String?,
    val price: String, // Usando String para evitar problemas de serialización con BigDecimal
    val uomId: Int,
    val isActive: Boolean
) {
    companion object {
        fun create(
            productId: Int,
            name: String,
            description: String?,
            price: BigDecimal,
            uomId: Int,
            isActive: Boolean
        ): ExternalProduct {
            require(productId >= 0) { "Product ID debe ser mayor o igual a 0" }
            require(name.isNotBlank()) { "Name no puede estar vacío" }
            require(uomId >= 0) { "UOM ID debe ser mayor o igual a 0" }

            return ExternalProduct(
                productId = productId,
                name = name.trim(),
                description = description?.trim(),
                price = price.toString(),
                uomId = uomId,
                isActive = isActive
            )
        }
    }
}
