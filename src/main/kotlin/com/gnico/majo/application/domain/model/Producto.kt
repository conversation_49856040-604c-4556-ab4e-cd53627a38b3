package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

@ConsistentCopyVisibility
data class Producto private constructor(
    val codigo: Int,
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedida: Id,
    val tipoIva: Id,
    val categoria: Id? = null,
    val precioUnitario: BigDecimal? = null,
    val stockActual: Int? = null,
    val activo: Boolean = true,
    val creadoEn: LocalDateTime? = null,
    val actualizadoEn: LocalDateTime? = null
) {
    init {
        require(codigo in 0..9999) { "El código debe ser un entero de 4 dígitos (0-9999)" }
    }

    /**
     * Crea una copia del producto con las fechas de creación y actualización establecidas
     */
    fun withTimestamps(creadoEn: LocalDateTime, actualizadoEn: LocalDateTime): Producto {
        return copy(creadoEn = creadoEn, actualizadoEn = actualizadoEn)
    }

    /**
     * Crea una copia del producto con la fecha de actualización establecida
     */
    fun withUpdatedTimestamp(actualizadoEn: LocalDateTime): Producto {
        return copy(actualizadoEn = actualizadoEn)
    }

    /**
     * Crea una copia del producto marcado como inactivo
     */
    fun markAsInactive(): Producto {
        return copy(activo = false, actualizadoEn = LocalDateTime.now())
    }

    companion object {
        fun create(
            codigo: Int,
            nombre: String,
            descripcion: String? = null,
            unidadMedida: Id,
            tipoIva: Id,
            categoria: Id? = null,
            precioUnitario: BigDecimal? = null,
            stockActual: Int? = null,
            activo: Boolean = true,
            creadoEn: LocalDateTime? = null,
            actualizadoEn: LocalDateTime? = null
            ): Producto {
            return Producto(
                codigo = codigo,
                nombre = nombre,
                descripcion = descripcion,
                unidadMedida = unidadMedida,
                tipoIva = tipoIva,
                categoria = categoria,
                precioUnitario = precioUnitario,
                stockActual = stockActual,
                activo = activo,
                creadoEn = creadoEn,
                actualizadoEn = actualizadoEn
            )
        }
    }


}
