package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import com.gnico.majo.infrastructure.config.ExternalDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.sql.SQLException

class JdbcExternalProductRepository : ExternalProductRepositoryPort {

    override suspend fun findAllProducts(): List<ExternalProduct> = withContext(Dispatchers.IO) {
        val products = mutableListOf<ExternalProduct>()

        try {
            ExternalDatabase.getConnection().use { connection ->
                val sql = """
                    SELECT p.product_id, p.name, p.description, p2.pricelist as "price", p.uom_id, p.isactive
                    FROM product p
                    INNER JOIN productprice p2 ON p.product_id = p2.product_id
                    WHERE p2.pricelist_version_id = 'lst1'
                """.trimIndent()

                connection.prepareStatement(sql).use { statement ->
                    statement.executeQuery().use { resultSet ->
                        while (resultSet.next()) {
                            val productId = resultSet.getInt("product_id")
                            val name = resultSet.getString("name")
                            val description = resultSet.getString("description")
                            val price = resultSet.getBigDecimal("price") ?: BigDecimal.ZERO
                            val uomId = resultSet.getInt("uom_id")
                            val isActive = resultSet.getBoolean("isactive")

                            if (!resultSet.wasNull()) {
                                products.add(ExternalProduct.create(productId, name, description, price, uomId, isActive))
                            }
                        }
                    }
                }
            }
        } catch (e: SQLException) {
            throw RuntimeException("Error al obtener productos de la base de datos externa", e)
        }

        return@withContext products
    }

    override suspend fun findProductById(productId: Int): ExternalProduct? = withContext(Dispatchers.IO) {
        try {
            ExternalDatabase.getConnection().use { connection ->
                val sql = """
                    SELECT p.product_id, p.name, p.description, p2.pricelist as "price", p.uom_id, p.isactive
                    FROM product p
                    INNER JOIN productprice p2 ON p.product_id = p2.product_id
                    WHERE p2.pricelist_version_id = 'lst1' AND p.product_id = ?
                """.trimIndent()

                connection.prepareStatement(sql).use { statement ->
                    statement.setInt(1, productId)
                    statement.executeQuery().use { resultSet ->
                        if (resultSet.next()) {
                            val id = resultSet.getInt("product_id")
                            val name = resultSet.getString("name")
                            val description = resultSet.getString("description")
                            val price = resultSet.getBigDecimal("price") ?: BigDecimal.ZERO
                            val uomId = resultSet.getInt("uom_id")
                            val isActive = resultSet.getBoolean("isactive")

                            if (!resultSet.wasNull()) {
                                return@withContext ExternalProduct.create(id, name, description, price, uomId, isActive)
                            }
                        }
                    }
                }
            }
        } catch (e: SQLException) {
            throw RuntimeException("Error al obtener producto $productId de la base de datos externa", e)
        }

        return@withContext null
    }
}
