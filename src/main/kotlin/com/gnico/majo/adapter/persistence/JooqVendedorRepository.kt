package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Vendedor
import com.gnico.majo.application.port.out.VendedorRepository
import com.gnico.majo.jooq.generated.tables.Vendedores.Companion.VENDEDORES
import org.jooq.impl.DSL

class JooqVendedorRepository : VendedorRepository {
    override suspend fun findById(id: Id): Vendedor? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(VENDEDORES)
                .where(VENDEDORES.ID.eq(id.value))
                .fetchOne()
                ?.let { record ->
                    Vendedor(
                        id = Id(record.id!!),
                        nombre = record.nombre!!
                    )
                }
        }
    }
}