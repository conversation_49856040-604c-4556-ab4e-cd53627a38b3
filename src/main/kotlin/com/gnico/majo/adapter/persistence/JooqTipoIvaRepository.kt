package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.TipoIva
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.jooq.generated.tables.TiposIva.Companion.TIPOS_IVA
import org.jooq.impl.DSL

/*class JooqTipoIvaRepository : TipoIvaRepository {
    override suspend fun findAll(): List<TipoIva> {
        return Database.dsl.selectFrom(TIPOS_IVA)
            .fetch()
            .map { record ->
                TipoIva(
                    id = Id(record.id!!),
                    porcentaje = record.porcentaje!!
                )
            }
    }
} */

class JooqTipoIvaRepository : TipoIvaRepository {
    private val cache by lazy {
        Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(TIPOS_IVA)
                .fetch()
                .map { TipoIva(Id(it.id!!), it.porcentaje!!) }
        }
    }
    override suspend fun findAll(): List<TipoIva> = cache
}