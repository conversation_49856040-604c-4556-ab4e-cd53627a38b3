package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Cliente
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.jooq.generated.tables.Clientes.Companion.CLIENTES
import org.jooq.impl.DSL

class JooqClienteRepository : ClienteRepository {
    override suspend fun findById(id: Id): Cliente? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CLIENTES)
                .where(CLIENTES.ID.eq(id.value))
                .fetchOne()
                ?.let { record ->
                    Cliente(
                        id = Id(record.id!!),
                        nombre = record.razonSocial!!,
                        cuit = record.cuit
                    )
                }
        }
    }
}