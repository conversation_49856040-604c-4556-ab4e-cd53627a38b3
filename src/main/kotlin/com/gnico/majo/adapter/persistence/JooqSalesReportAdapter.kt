package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.SalesSummary
import org.jooq.impl.DSL
import java.math.BigDecimal
import java.time.LocalDateTime

class JooqSalesReportAdapter : SalesReportPort {
    override fun getSalesSummary(
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): SalesSummary {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            println("*gets sales summary*")
            // Aquí iría la lógica real para obtener el resumen de ventas desde la base de datos
            SalesSummary(1, 1, 1, BigDecimal(1), BigDecimal(1))
        }
    }
}