package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class SaleRequest(
    val vendedorId: Int,
    val clienteId: Int?,
    val items: List<SaleItemRequest>
)

@Serializable
data class SaleItemRequest(
    val productoId: Int,
    val cantidad: Double,
    val precioUnitario: Double,
    val tipoIvaId: Int
)

@Serializable
data class SaleResponse(val saleId: Int)

@Serializable
data class ErrorResponse(val message: String)
