package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.`in`.SaleService

class SaleController(
    private val saleService: SaleService
) {
    suspend fun createSale(request: SaleRequest): Id {
        return saleService.createSale(
            clienteId = request.clienteId,
            vendedorId = request.vendedorId,
            itemsRequest = request.items
        )
    }
}