package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class ProductoRequest(
    val codigo: Int, // Ahora es Int de 4 dígitos
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedidaId: Int,
    val tipoIvaId: Int,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val stockActual: Int? = null,
    val activo: Boolean = true
)

@Serializable
data class ProductoUpdateRequest(
    val codigo: Int, // Ahora usamos codigo como identificador
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedidaId: Int,
    val tipoIvaId: Int,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val stockActual: Int? = null,
    val activo: Boolean = true
)

@Serializable
data class ProductoResponse(
    val codigo: Int, // Ahora codigo es la clave primaria
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedidaId: Int,
    val tipoIvaId: Int,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val stockActual: Int? = null,
    val activo: Boolean
)

@Serializable
data class ProductoListResponse(
    val productos: List<ProductoResponse>
)

@Serializable
data class ProductoCodigoResponse(
    val codigo: Int
)
