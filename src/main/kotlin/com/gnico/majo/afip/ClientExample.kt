package com.gnico.majo.afip

import fev1.dif.afip.gov.ar.*


fun main() {
    val service : Service = Service()
    val port : ServiceSoap = service.serviceSoap

    try {
        val response : DummyResponse = port.feDummy()
        println("Response: $response")
        println("AppServer: ${response.appServer}")
        println("DbServer: ${response.dbServer}")
        println("AuthServer: ${response.authServer}")

    } catch (ex : Exception) {
        println("Error: ${ex.message}")
        ex.printStackTrace()
    }

}

/*fun solicitarCAE() {
    val service = Service()
    val port = service.serviceSoap

    try {
        // 1. Create authentication request
        val auth = FEAuthRequest().apply {
            token = "your-token-here"  // From WSAA authentication
            sign = "your-signature-here"  // From WSAA authentication
            cuit = 20111111112L  // Replace with your CUIT
        }

        // 2. Create invoice details
        val invoice = FECAEDetRequest().apply {
            concepto = 1  // Products
            docTipo = 80  // CUIT
            docNro = 20111111112L  // Customer document number
            cbteDesde = 1L  // Invoice number from
            cbteHasta = 1L  // Invoice number to
            cbteFch = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
            impTotal = 100.00  // Total amount
            impTotConc = 0.00  // Non-taxable amount
            impNeto = 82.64    // Net amount
            impIVA = 17.36     // VAT amount
            impTrib = 0.00     // Other taxes
            monId = "PES"      // Currency (Argentine Peso)
            monCotiz = 1.0     // Exchange rate
        }

        // 3. Add VAT details if needed
        val vat = ArrayOfAlicIva().apply {
            alicIva.add(AlicIva().apply {
                id = 5  // 21% VAT rate
                baseImp = 82.64  // Net amount
                importe = 17.36  // VAT amount
            })
        }
        invoice.iva = vat

        // 4. Create the main request
        val request = FECAERequest().apply {
            feCabReq = FECAECabRequest().apply {
                cantReg = 1     // Number of invoices in request
                ptoVta = 1      // Point of sale
                cbteTipo = 1    // Invoice type (A)
            }
            feDetReq.add(invoice)
        }

        // 5. Call the service
        val response = port.fecaeSolicitar(auth, request)

        // 6. Process the response
        println("Result: ${response.header.resultado}")
        if (response.header.resultado == "A") {
            // Approved
            val cae = response.feDetResp.get(0).cae
            val caeFchVto = response.feDetResp.get(0).caeFchVto
            println("CAE: $cae")
            println("CAE expiration: $caeFchVto")
        } else {
            // Rejected
            response.errors?.err?.forEach { error ->
                println("Error ${error.code}: ${error.msg}")
            }
        }

    } catch (ex: Exception) {
        println("Error: ${ex.message}")
        ex.printStackTrace()
    }
}
*/