package com.gnico.majo.afip

import java.time.ZonedDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

/**
 * This object is responsible for generating time strings in the format required by the AFIP WSAA service.
 * The time is generated in the -03:00 timezone, which is the timezone used by Argentina.
 */
object TimeBuilder {
    private val zoneOffset = ZoneOffset.of("-03:00")
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd") // Nuevo formatter para la fecha


    fun getCurrentTime(): String = ZonedDateTime.now(zoneOffset).format(formatter)
    fun getTenMinutesLater(): String = ZonedDateTime.now(zoneOffset).plusMinutes(10).format(formatter)
    fun getTenMinutesBefore(): String = ZonedDateTime.now(zoneOffset).minusMinutes(10).format(formatter)
    fun getCurrentDate(): String = ZonedDateTime.now(zoneOffset).format(dateFormatter)
}