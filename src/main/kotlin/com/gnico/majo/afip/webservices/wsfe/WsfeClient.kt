package com.gnico.majo.afip.webservices.wsfe

import com.gnico.majo.afip.TimeBuilder
import fev1.dif.afip.gov.ar.*

fun main() {
    val client = WsfeClient()
    client.requestCAE()
}

class WsfeClient {

    fun requestCAE() {
        val service = Service()
        val port = service.serviceSoap

        try {
            // 1. Create authentication request
            val auth = FEAuthRequest().apply {
                token = "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/Pgo8c3NvIHZlcnNpb249IjIuMCI+CiAgICA8aWQgc3JjPSJDTj13c2FhaG9tbywgTz1BRklQLCBDPUFSLCBTRVJJQUxOVU1CRVI9Q1VJVCAzMzY5MzQ1MDIzOSIgZHN0PSJDTj13c2ZlLCBPPUFGSVAsIEM9QVIiIHVuaXF1ZV9pZD0iMjY4NTIxNTgzNCIgZ2VuX3RpbWU9IjE3NDU4NzgzOTMiIGV4cF90aW1lPSIxNzQ1OTIxNjUzIi8+CiAgICA8b3BlcmF0aW9uIHR5cGU9ImxvZ2luIiB2YWx1ZT0iZ3JhbnRlZCI+CiAgICAgICAgPGxvZ2luIGVudGl0eT0iMzM2OTM0NTAyMzkiIHNlcnZpY2U9IndzZmUiIHVpZD0iU0VSSUFMTlVNQkVSPUNVSVQgMjAzNDkyNDk5MDIsIENOPWduaWNvIiBhdXRobWV0aG9kPSJjbXMiIHJlZ21ldGhvZD0iMjIiPgogICAgICAgICAgICA8cmVsYXRpb25zPgogICAgICAgICAgICAgICAgPHJlbGF0aW9uIGtleT0iMjAzNDkyNDk5MDIiIHJlbHR5cGU9IjQiLz4KICAgICAgICAgICAgPC9yZWxhdGlvbnM+CiAgICAgICAgPC9sb2dpbj4KICAgIDwvb3BlcmF0aW9uPgo8L3Nzbz4K"
                sign = "dm4N/zNXuUR7j2fvk+VKOjIo5NlbJva0ZhCqYo07xt5TYKTgHEAdMq28pWAKdka8oQawQ07GVCdj3O8O/AKuXx3jTHuLcmqdnlDxZDlTluUMALzN7we/02BAFxb4L3cXJfsEcDlJolokx0qH/e5XDYTswM0iJlvq4hP+utk63kk="
                cuit = 20349249902L
            }

            // 2. Create invoice details
            val invoice = FECAEDetRequest().apply {
                concepto = 1  // Products
                docTipo = 80  // CUIT
                docNro = 20111111112L  // Customer document number
                cbteDesde = 3 // Invoice number from
                cbteHasta = 3  // Invoice number to
                cbteFch = TimeBuilder.getCurrentDate()
                impTotal = 100.00  // importe total
                impTotConc = 0.00  // importe non-taxable
                impNeto = 82.64    // importe neto
                impIVA = 17.36     // importe IVA
                impTrib = 0.00     // otros impuestos
                monId = "PES"      // Currency (Argentine Peso)
                monCotiz = 1.0     // Exchange rate
            }

            // 3. Add VAT details if needed
            val vat = ArrayOfAlicIva().apply {
                alicIva.add(AlicIva().apply {
                    id = 5  // 21% VAT rate
                    baseImp = 82.64  // Importe neto
                    importe = 17.36  // Importe IVA
                })
            }
            invoice.iva = vat

            // 4. Create the main request
            val request = FECAERequest().apply {
                feCabReq = FECAECabRequest().apply {
                    cantReg = 1     // Number of invoices in request
                    ptoVta = 1      // Point of sale
                    cbteTipo = 1    // Invoice type (A)
                }
                //feDetReq.add(invoice)
                feDetReq = ArrayOfFECAEDetRequest().apply {
                    fecaeDetRequest.add(invoice)
                }
            }

            // 5. Call the service
            val response = port.fecaeSolicitar(auth, request)

            response?.let {
                // Check for errors
                if (it.errors?.getErr()?.isNotEmpty() == true) {
                    println("Errors found in response:")
                    it.errors.getErr().forEach { err ->
                        println("Error Code: ${err.code}, Message: ${err.msg}")
                    }
                    return
                }

                // Check for events
                if (it.events?.getEvt()?.isNotEmpty() == true) {
                    println("Events found in response:")
                    it.events.getEvt().forEach { evt ->
                        println("Event Code: ${evt.code}, Message: ${evt.msg}")
                    }
                }

                // Process header response
                it.feCabResp?.let { cabResp ->
                    println("Header Response: CantReg=${cabResp.cantReg}, PtoVta=${cabResp.ptoVta}, CbteTipo=${cabResp.cbteTipo}")
                }

                // Process detail response
                it.feDetResp?.getFECAEDetResponse()?.forEach { detResp ->
                    println("Detail Response:")
                    println("Invoice Number: ${detResp.cbteDesde}")
                    println("Result: ${detResp.resultado}") // A (Approved) or R (Rejected)

                    // Check CAE and expiration date
                    if (detResp.resultado == "A") {
                        println("CAE: ${detResp.cae}")
                        println("CAE Expiration: ${detResp.caeFchVto}")
                    } else {
                        println("Invoice rejected. Checking observations:")
                        detResp.observaciones?.getObs()?.forEach { obs ->
                            println("Observation Code: ${obs.code}, Message: ${obs.msg}")
                        }
                    }
                }
            } ?: run {
                println("No response received from the service.")
            }


        } catch (ex: Exception) {
            println("Error: ${ex.message}")
            ex.printStackTrace()
        }

    }

}