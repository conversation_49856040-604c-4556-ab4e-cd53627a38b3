package com.gnico.majo.infrastructure.config

import com.gnico.majo.adapter.persistence.JooqClienteRepository
import com.gnico.majo.adapter.persistence.JooqProductoRepository
import com.gnico.majo.adapter.persistence.JooqSaleRepository
import com.gnico.majo.adapter.persistence.JooqSalesReportAdapter
import com.gnico.majo.adapter.persistence.JooqTipoIvaRepository
import com.gnico.majo.adapter.persistence.JooqVendedorRepository
import com.gnico.majo.adapter.persistence.JdbcExternalProductRepository
import com.gnico.majo.adapter.printer.EscPosPrinterAdapter
import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.ExternalProductController
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.`in`.ExternalProductService
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.application.port.out.VendedorRepository
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.usecase.SaleServiceImpl
import com.gnico.majo.application.usecase.ExternalProductServiceImpl
import org.koin.dsl.module


/**
 * Main application module for dependency injection
 */
val appModule = module {
    // Repositories
    single<SaleRepositoryPort> { JooqSaleRepository() }
    single<VendedorRepository> { JooqVendedorRepository() }
    single<ClienteRepository> { JooqClienteRepository() }
    single<TipoIvaRepository> { JooqTipoIvaRepository() }
    single<SalesReportPort> { JooqSalesReportAdapter() }
    single<ProductoRepositoryPort> { JooqProductoRepository() }
    single<ExternalProductRepositoryPort> { JdbcExternalProductRepository() }

    // Services
    single<PrinterPort> { EscPosPrinterAdapter() }
    single<SaleService> {
        SaleServiceImpl(
            saleRepository = get(),
            printer = get(),
            salesReport = get(),
            vendedorRepository = get(),
            clienteRepository = get(),
            tipoIvaRepository = get()
        )
    }
    single<ProductoService> { ProductoServiceImpl(productoRepository = get()) }
    single<ExternalProductService> {
        ExternalProductServiceImpl(
            externalProductRepository = get(),
            productoRepository = get()
        )
    }

    // Controllers
    single { SaleController(saleService = get()) }
    single { ProductoController(productoService = get()) }
    single { ExternalProductController(externalProductService = get()) }
}
